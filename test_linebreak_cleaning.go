package main

import (
	"encoding/json"
	"fmt"
	"go-api-solve/internal/utils"
	"strings"
)

func main() {
	// 测试包含换行符的Qwen响应数据
	testCases := []struct {
		name     string
		qwenData string
	}{
		{
			name: "包含\\n换行符的题干",
			qwenData: `{
				"question_type": "单选题",
				"question_text": "(单选题)08、雾天跟车行驶,\n应如何安全驾驶?",
				"question_num": "8",
				"A": "加大跟车距离,降低行驶速度",
				"B": "提前开启雾灯、危险报警闪光灯",
				"C": "以前车尾灯作为判断安全距离的参照物",
				"D": "按喇叭提示行车位置"
			}`,
		},
		{
			name: "包含\\r\\n换行符的题干",
			qwenData: `{
				"question_type": "判断题",
				"question_text": "(判断题)05、等17分钟后\r\n如果没问题就可以走了。",
				"question_num": "5",
				"Y": "正确",
				"N": "错误"
			}`,
		},
		{
			name: "包含多个换行符的题干",
			qwenData: `{
				"question_type": "多选题",
				"question_text": "多选题)12、\n\n雾天跟车行驶,\r\n\n应如何安全驾驶?",
				"question_num": "12",
				"A": "加大跟车距离,降低行驶速度",
				"B": "提前开启雾灯、危险报警闪光灯",
				"C": "以前车尾灯作为判断安全距离的参照物",
				"D": "按喇叭提示行车位置"
			}`,
		},
		{
			name: "正常无换行符的题干",
			qwenData: `{
				"question_type": "单选题",
				"question_text": "(单选题)03、雾天跟车行驶,应如何安全驾驶?",
				"question_num": "3",
				"A": "加大跟车距离,降低行驶速度",
				"B": "提前开启雾灯、危险报警闪光灯",
				"C": "以前车尾灯作为判断安全距离的参照物",
				"D": "按喇叭提示行车位置"
			}`,
		},
	}

	fmt.Println("=== FormatQwenData 换行符清洗测试 ===\n")

	for i, tc := range testCases {
		fmt.Printf("测试案例 %d: %s\n", i+1, tc.name)
		fmt.Printf("原始数据: %s\n", tc.qwenData)

		// 调用FormatQwenData进行处理
		result, err := utils.FormatQwenData(tc.qwenData)
		if err != nil {
			fmt.Printf("错误: %v\n", err)
		} else {
			// 格式化输出结果
			resultJSON, _ := json.MarshalIndent(result, "", "  ")
			fmt.Printf("处理结果: %s\n", string(resultJSON))
			fmt.Printf("清洗后的题干: %s\n", result.QuestionText)
		}
		fmt.Println(strings.Repeat("-", 80))
	}
}
